spring.application.name=movies-api

# Server Configuration
server.port=8080
server.servlet.context-path=/

# OMDB API Configuration
omdb.api.url=http://www.omdbapi.com/
omdb.api.key=62eec860

# Details Service Configuration (OMDB API)
details.service.host=www.omdbapi.com
details.service.port=80
details.service.url=http://${details.service.host}

# Search Service Configuration
search.service.host=localhost
search.service.port=8080
search.service.url=http://${search.service.host}:${search.service.port}

# Other Configuration
b.w..a.p.p.n.o.d.e..n.a.m.e=
b.w..d.e.p.l.o.y.m.e.n.t.u.n.i.t..n.a.m.e=
b.w..h.o.s.t..n.a.m.e=localhost
b.w..d.e.p.l.o.y.m.e.n.t.u.n.i.t..v.e.r.s.i.o.n=
b.w..m.o.d.u.l.e..v.e.r.s.i.o.n=
b.w..c.l.o.u.d..p.o.r.t=8080
b.w..m.o.d.u.l.e..n.a.m.e=
service.port=9090

# Logging Configuration
logging.level.com.example.movies=DEBUG
logging.level.org.springframework.web=INFO

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
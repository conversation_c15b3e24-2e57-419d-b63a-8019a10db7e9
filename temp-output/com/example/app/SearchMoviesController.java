package com.example.app;

import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
public class SearchMoviesController {
        private static final Logger logger = LoggerFactory.getLogger(SearchMoviesController.class);

    @Autowired
    private SearchMoviesService searchMoviesService;
        @GetMapping("/movies")
    public ResponseEntity<OMDBSearchElement> get(@RequestParam("searchString") String searchString) {
        try {
            OMDBSearchElement result = searchMoviesService.get(searchString);
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error in get: " + e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
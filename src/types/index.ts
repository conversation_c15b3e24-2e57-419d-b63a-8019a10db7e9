/**
 * XSD Schema 相关类型定义
 */
export interface XSDSchema {
  schema: {
    $: {
      targetNamespace?: string;
      elementFormDefault?: string;
    };
    complexType?: XSDComplexType[];
    element?: XSDElement[];
  };
}

export interface XSDComplexType {
  $: {
    name: string;
  };
  sequence?: XSDSequence[];
}

export interface XSDSequence {
  element: XSDElement[];
}

export interface XSDElement {
  $: {
    name: string;
    type?: string;
    minOccurs?: string;
    maxOccurs?: string;
  };
  complexType?: XSDComplexType[];
}

/**
 * 解析后的类型信息
 */
export interface ParsedClass {
  name: string;
  namespace: string;
  fields: ParsedField[];
  isRootElement?: boolean;
}

export interface ParsedField {
  name: string;
  type: string;
  javaType: string;
  isOptional: boolean;
  isArray: boolean;
  isComplexType: boolean;
  nestedClass?: ParsedClass;
}

/**
 * Java 代码生成选项
 */
export interface JavaGenerationOptions {
  packageName: string;
  outputDir: string;
  useJSR303Validation?: boolean;
  useLombok?: boolean;
  useJacksonAnnotations?: boolean;
  includeConstructors?: boolean;
  includeToString?: boolean;
}

/**
 * CLI 配置
 */
export interface CLIConfig {
  inputDir: string;
  outputDir: string;
  packageName: string;
  options: JavaGenerationOptions;
}

/**
 * 差异对比结果
 */
export interface ComparisonResult {
  className: string;
  differences: Difference[];
}

export interface Difference {
  type: 'missing_field' | 'extra_field' | 'type_mismatch' | 'annotation_mismatch';
  field: string;
  expected?: string;
  actual?: string;
  description: string;
}

/**
 * BWP (BPEL Process) 相关类型定义
 */
export interface BWPProcess {
  $: {
    name: string;
    targetNamespace: string;
    exitOnStandardFault?: string;
    suppressJoinFailure?: string;
  };
  processInfo?: BWPProcessInfo;
  processInterface?: BWPProcessInterface;
  variables?: BWPVariables;
  partnerLinks?: BWPPartnerLinks;
  scope?: BWPScope;
  imports?: BWPImport | BWPImport[];
  namespaceRegistry?: BWPNamespaceRegistry;
}

export interface BWPProcessInfo {
  $: {
    callable?: string;
    createdBy?: string;
    createdOn?: string;
    description?: string;
    extraErrorVars?: string;
    modifiers?: string;
    productVersion?: string;
    scalable?: string;
    singleton?: string;
    stateless?: string;
    type?: string;
  };
}

export interface BWPProcessInterface {
  $: {
    context?: string;
    input?: string;
    output?: string;
  };
}

export interface BWPVariables {
  variable: BWPVariable[];
}

export interface BWPVariable {
  $: {
    name: string;
    element?: string;
    messageType?: string;
    'sca-bpel:internal'?: string;
    'tibex:parameter'?: string;
  };
}

export interface BWPPartnerLinks {
  partnerLink: BWPPartnerLink[];
}

export interface BWPPartnerLink {
  $: {
    name: string;
    partnerLinkType: string;
    partnerRole?: string;
    'sca-bpel:ignore'?: string;
    'sca-bpel:reference'?: string;
    'sca-bpel:wiredByImpl'?: string;
  };
  referenceBinding?: BWPReferenceBinding[];
}

export interface BWPReferenceBinding {
  binding: BWPBinding[];
}

export interface BWPBinding {
  referenceBinding: {
    $: {
      name: string;
      'xsi:type': string;
    };
    'sca:interface.wsdl'?: any;
    'scaext:binding'?: BWPRestBinding[];
  };
}

export interface BWPRestBinding {
  $: {
    basePath?: string;
    connector?: string;
    docBasePath?: string;
    docResourcePath?: string;
    implementation?: string;
    name?: string;
    path?: string;
    structuredData?: string;
    technologyVersion?: string;
    'xsi:type': string;
  };
  operation?: BWPRestOperation[];
}

export interface BWPRestOperation {
  $: {
    httpMethod: string;
    operationName: string;
    requestEntityProcessing?: string;
    responseStyle?: string;
  };
  parameters?: BWPRestParameter[];
  clientFormat?: string[];
}

export interface BWPRestParameter {
  parameterMapping: BWPParameterMapping[];
}

export interface BWPParameterMapping {
  $: {
    dataType: string;
    parameterName: string;
    parameterType: string;
    required?: string;
  };
}

export interface BWPScope {
  $: {
    name: string;
  };
  flow?: BWPFlow;
}

export interface BWPFlow {
  $: {
    name: string;
  };
  links?: BWPLinks;
  extensionActivity?: BWPExtensionActivity[];
  invoke?: BWPInvoke[];
}

export interface BWPLinks {
  link: BWPLink[];
}

export interface BWPLink {
  $: {
    name: string;
    'tibex:linkType'?: string;
  };
}

export interface BWPExtensionActivity {
  'tibex:receiveEvent'?: BWPReceiveEvent[];
  'tibex:activityExtension'?: BWPActivityExtension[];
}

export interface BWPReceiveEvent {
  $: {
    createInstance?: string;
    eventTimeout?: string;
    name: string;
    'tibex:xpdlId'?: string;
    variable?: string;
  };
  sources?: BWPSources;
  eventSource?: BWPEventSource;
}

export interface BWPActivityExtension {
  $: {
    expression?: string;
    expressionLanguage?: string;
    inputVariable?: string;
    name: string;
    'tibex:xpdlId'?: string;
  };
  targets?: BWPTargets;
  sources?: BWPSources;
  inputBindings?: BWPInputBindings;
  config?: BWPConfig;
}

export interface BWPInvoke {
  $: {
    inputVariable?: string;
    name: string;
    operation?: string;
    outputVariable?: string;
    partnerLink?: string;
    portType?: string;
    'tibex:xpdlId'?: string;
  };
  'tibex:inputBinding'?: string;
  inputBindings?: BWPInputBindings;
  targets?: BWPTargets;
  sources?: BWPSources;
}

export interface BWPSources {
  source: BWPSource[];
}

export interface BWPSource {
  $: {
    linkName: string;
  };
}

export interface BWPTargets {
  target: BWPTarget[];
}

export interface BWPTarget {
  $: {
    linkName: string;
  };
}

export interface BWPEventSource {
  'tibex:StartEvent'?: any[];
}

export interface BWPInputBindings {
  inputBinding: BWPInputBinding[];
}

export interface BWPInputBinding {
  $: {
    expression: string;
    expressionLanguage: string;
  };
}

export interface BWPConfig {
  'bwext:BWActivity': BWPBWActivity;
}

export interface BWPBWActivity {
  $: {
    activityTypeID: string;
    version?: string;
  };
  activityConfig?: BWPActivityConfig;
}

export interface BWPActivityConfig {
  properties: BWPProperty[];
}

export interface BWPProperty {
  $: {
    name: string;
    'xsi:type': string;
  };
  type?: any;
  value?: any;
}

export interface BWPImport {
  $: {
    importType: string;
    namespace: string;
  };
}

export interface BWPNamespaceRegistry {
  $: {
    enabled: string;
  };
  namespaceItem: BWPNamespaceItem[];
}

export interface BWPNamespaceItem {
  $: {
    namespace: string;
    prefix: string;
  };
}

/**
 * 解析后的业务流程信息
 */
export interface ParsedBWPProcess {
  name: string;
  namespace: string;
  processInfo: {
    callable: boolean;
    stateless: boolean;
    type: string;
    modifiers: string;
  };
  interface: {
    inputType: string;
    outputType: string;
    inputNamespace?: string;
    outputNamespace?: string;
  };
  activities: ParsedActivity[];
  variables: ParsedVariable[];
  partnerLinks: ParsedPartnerLink[];
  restEndpoints: ParsedRestEndpoint[];
}

export interface ParsedActivity {
  id: string;
  name: string;
  type: 'start' | 'end' | 'invoke' | 'log' | 'transform';
  inputVariable?: string;
  outputVariable?: string;
  expression?: string;
  expressionLanguage?: string;
  partnerLink?: string;
  operation?: string;
  portType?: string;
  links: {
    sources: string[];
    targets: string[];
  };
  config?: {
    activityTypeID: string;
    version?: string;
  };
}

export interface ParsedVariable {
  name: string;
  type: 'element' | 'messageType';
  dataType: string;
  namespace?: string;
  isInternal: boolean;
  parameterType?: 'in' | 'out';
}

export interface ParsedPartnerLink {
  name: string;
  partnerLinkType: string;
  role: string;
  restBinding?: ParsedRestBinding;
}

export interface ParsedRestBinding {
  basePath: string;
  path: string;
  connector: string;
  docBasePath: string;
  operations: ParsedRestOperation[];
}

export interface ParsedRestOperation {
  name: string;
  httpMethod: string;
  parameters: ParsedRestOperationParameter[];
  clientFormat: string;
}

export interface ParsedRestOperationParameter {
  name: string;
  dataType: string;
  parameterType: string;
  required: boolean;
}

export interface ParsedRestEndpoint {
  path: string;
  method: string;
  operationName: string;
  inputType: string;
  outputType: string;
  parameters: ParsedRestOperationParameter[];
  connector?: string; // HTTP 客户端连接器名称
  baseUrl?: string; // 外部服务基础 URL
}

/**
 * Java 代码生成上下文
 */
export interface JavaGenerationContext {
  packageName: string;
  className: string;
  imports: Set<string>;
  annotations: string[];
  methods: JavaMethod[];
  fields: JavaField[];
}

export interface JavaMethod {
  name: string;
  returnType: string;
  parameters: JavaParameter[];
  annotations: string[];
  body: string;
  visibility: 'public' | 'private' | 'protected';
}

export interface JavaParameter {
  name: string;
  type: string;
  annotations: string[];
}

export interface JavaField {
  name: string;
  type: string;
  annotations: string[];
  visibility: 'public' | 'private' | 'protected' | 'private static final';
  isFinal?: boolean;
  isStatic?: boolean;
  initialValue?: string;
}

/**
 * OpenAPI/Swagger 相关类型定义
 */
export interface SwaggerSpec {
  swagger?: string;
  openapi?: string;
  info: {
    title: string;
    version: string;
    description?: string;
  };
  host?: string;
  basePath?: string;
  schemes?: string[];
  consumes?: string[];
  produces?: string[];
  paths: Record<string, PathItem>;
  definitions?: Record<string, SchemaObject>;
  components?: {
    schemas?: Record<string, SchemaObject>;
  };
}

export interface PathItem {
  get?: Operation;
  post?: Operation;
  put?: Operation;
  delete?: Operation;
  patch?: Operation;
  options?: Operation;
  head?: Operation;
}

export interface Operation {
  tags?: string[];
  summary?: string;
  description?: string;
  operationId?: string;
  consumes?: string[];
  produces?: string[];
  parameters?: Parameter[];
  responses: Record<string, Response>;
  security?: SecurityRequirement[];
}

export interface Parameter {
  name: string;
  in: 'query' | 'header' | 'path' | 'formData' | 'body';
  description?: string;
  required?: boolean;
  type?: string;
  format?: string;
  schema?: SchemaObject;
}

export interface Response {
  description: string;
  schema?: SchemaObject;
  headers?: Record<string, Header>;
}

export interface Header {
  description?: string;
  type: string;
  format?: string;
}

export interface SchemaObject {
  type?: string;
  format?: string;
  items?: SchemaObject;
  properties?: Record<string, SchemaObject>;
  required?: string[];
  $ref?: string;
  allOf?: SchemaObject[];
  oneOf?: SchemaObject[];
  anyOf?: SchemaObject[];
}

export interface SecurityRequirement {
  [name: string]: string[];
}

/**
 * API 测试相关类型定义
 */
export interface ApiTestCase {
  name: string;
  method: string;
  path: string;
  parameters: TestParameter[];
  expectedResponse: ExpectedResponse;
  description?: string;
}

export interface TestParameter {
  name: string;
  type: string;
  value: any;
  required: boolean;
  location: 'query' | 'header' | 'path' | 'body' | 'formData';
}

export interface ExpectedResponse {
  statusCode: number;
  contentType?: string;
  schema?: SchemaObject;
  properties?: string[];
}

/**
 * API 测试生成选项
 */
export interface ApiTestGenerationOptions {
  packageName: string;
  outputDir: string;
  testFramework: 'junit5' | 'junit4';
  useSpringBootTest: boolean;
  generateIntegrationTests: boolean;
  generateUnitTests: boolean;
  includeNegativeTests: boolean;
  baseUrl?: string;
}

/**
 * Spring Boot 应用启动配置
 */
export interface SpringBootStartupConfig {
  projectPath: string;
  port?: number;
  profile?: string;
  jvmArgs?: string[];
  timeout?: number;
}

/**
 * 应用启动结果
 */
export interface ApplicationStartupResult {
  success: boolean;
  port: number;
  pid?: number;
  error?: string;
  logs?: string[];
}

package com.example.movies;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class SortMoviesService {
    private static final Logger logger = LoggerFactory.getLogger(SortMoviesService.class);
        @Autowired
    private RestTemplate restTemplate;

    @Value("${omdb.api.url}")
    private String omdbApiUrl;

    @Value("${omdb.api.key}")
    private String apiKey;

    public Details get(String i, String apikey) {
        logger.info("Executing get with parameters: {}, {}", i, apikey);

        try {
            // Call OMDB API for movie details
            String url = omdbApiUrl + "?i=" + i + "&apikey=" + apikey;
            logger.debug("Calling OMDB API for details: {}", url);

            Details result = restTemplate.getForObject(url, Details.class);

            logger.info("Successfully completed get");
            return result;
        } catch (Exception e) {
            logger.error("Error in get: " + e.getMessage(), e);
            throw new RuntimeException("Service call failed", e);
        }
    }
}